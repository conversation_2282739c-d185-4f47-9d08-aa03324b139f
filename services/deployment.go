package services

import (
	"fmt"
	"time"

	"jenkins-deploy-platform/database"
	"jenkins-deploy-platform/models"
	"jenkins-deploy-platform/utils"
)

// LogDeployment 记录部署日志
func LogDeployment(deploymentID uint, stage, message, level string) {
	log := models.DeploymentLog{
		DeploymentID: deploymentID,
		Stage:        stage,
		Message:      message,
		Level:        level,
	}
	database.DB.Create(&log)
}

// StartDeployment 开始部署（无参数）
func StartDeployment(deploymentID uint) {
	StartDeploymentWithParams(deploymentID, nil)
}

// StartDeploymentWithParams 开始部署（带参数）
func StartDeploymentWithParams(deploymentID uint, parameters map[string]string) {
	var deployment models.Deployment
	if err := database.DB.Preload("Project").First(&deployment, deploymentID).Error; err != nil {
		LogDeployment(deploymentID, "error", "Failed to load deployment: "+err.Error(), "error")
		return
	}

	// 更新部署状态
	deployment.Status = "deploying"
	database.DB.Save(&deployment)

	LogDeployment(deploymentID, "start", "Starting deployment process", "info")

	// 创建Jenkins客户端
	client := utils.NewJenkinsClient()

	// 获取项目参数
	var projectParams []models.JenkinsParameter
	database.DB.Where("project_id = ?", deployment.ProjectID).Find(&projectParams)

	// 合并参数
	buildParams := make(map[string]string)
	
	// 设置默认参数
	for _, param := range projectParams {
		buildParams[param.Name] = param.Value
	}
	
	// 覆盖用户提供的参数
	for key, value := range parameters {
		buildParams[key] = value
	}

	// 添加系统参数
	buildParams["DEPLOY_VERSION"] = deployment.Version
	buildParams["DEPLOY_BRANCH"] = deployment.Branch
	buildParams["DEPLOY_ENVIRONMENT"] = deployment.Environment
	if deployment.CommitHash != "" {
		buildParams["COMMIT_HASH"] = deployment.CommitHash
	}

	LogDeployment(deploymentID, "jenkins", "Triggering Jenkins build", "info")

	// 触发Jenkins构建
	buildNumber, err := client.BuildJob(deployment.Project.JenkinsJob, buildParams)
	if err != nil {
		LogDeployment(deploymentID, "error", "Failed to trigger Jenkins build: "+err.Error(), "error")
		updateDeploymentStatus(deploymentID, "failed")
		return
	}

	// 更新构建信息
	deployment.JenkinsBuild = buildNumber
	deployment.BuildURL = fmt.Sprintf("%s/job/%s/%d/", client.BaseURL, deployment.Project.JenkinsJob, buildNumber)
	database.DB.Save(&deployment)

	LogDeployment(deploymentID, "jenkins", fmt.Sprintf("Jenkins build #%d started", buildNumber), "info")

	// 监控构建状态
	go monitorBuild(deploymentID, deployment.Project.JenkinsJob, buildNumber)
}

// monitorBuild 监控Jenkins构建状态
func monitorBuild(deploymentID uint, jobName string, buildNumber int) {
	client := utils.NewJenkinsClient()
	
	for {
		time.Sleep(10 * time.Second) // 每10秒检查一次

		build, err := client.GetBuildStatus(jobName, buildNumber)
		if err != nil {
			LogDeployment(deploymentID, "error", "Failed to get build status: "+err.Error(), "error")
			continue
		}

		LogDeployment(deploymentID, "jenkins", fmt.Sprintf("Build status: %s, Building: %t", build.Result, build.Building), "info")

		if !build.Building {
			// 构建完成
			if build.Result == "SUCCESS" {
				LogDeployment(deploymentID, "success", "Jenkins build completed successfully", "info")
				updateDeploymentStatus(deploymentID, "success")
				
				// 获取构建日志
				go saveBuildLog(deploymentID, jobName, buildNumber)
			} else {
				LogDeployment(deploymentID, "error", fmt.Sprintf("Jenkins build failed with result: %s", build.Result), "error")
				updateDeploymentStatus(deploymentID, "failed")
				
				// 获取构建日志
				go saveBuildLog(deploymentID, jobName, buildNumber)
			}
			break
		}
	}
}

// updateDeploymentStatus 更新部署状态
func updateDeploymentStatus(deploymentID uint, status string) {
	var deployment models.Deployment
	if err := database.DB.First(&deployment, deploymentID).Error; err != nil {
		return
	}

	deployment.Status = status
	if status == "success" || status == "failed" {
		now := time.Now()
		deployment.DeployedAt = &now
	}

	database.DB.Save(&deployment)
}

// saveBuildLog 保存Jenkins构建日志
func saveBuildLog(deploymentID uint, jobName string, buildNumber int) {
	client := utils.NewJenkinsClient()
	
	log, err := client.GetBuildLog(jobName, buildNumber)
	if err != nil {
		LogDeployment(deploymentID, "error", "Failed to get build log: "+err.Error(), "error")
		return
	}

	// 将日志分段保存（避免单条记录过大）
	lines := splitLog(log, 1000) // 每1000个字符一段
	for i, line := range lines {
		LogDeployment(deploymentID, "build-log", fmt.Sprintf("Build log part %d: %s", i+1, line), "info")
	}
}

// splitLog 分割日志
func splitLog(log string, chunkSize int) []string {
	var chunks []string
	for len(log) > chunkSize {
		chunks = append(chunks, log[:chunkSize])
		log = log[chunkSize:]
	}
	if len(log) > 0 {
		chunks = append(chunks, log)
	}
	return chunks
}

// GetDeploymentStatistics 获取部署统计信息
func GetDeploymentStatistics() map[string]interface{} {
	stats := make(map[string]interface{})

	// 总部署次数
	var totalDeployments int64
	database.DB.Model(&models.Deployment{}).Count(&totalDeployments)
	stats["total_deployments"] = totalDeployments

	// 成功部署次数
	var successDeployments int64
	database.DB.Model(&models.Deployment{}).Where("status = ?", "success").Count(&successDeployments)
	stats["success_deployments"] = successDeployments

	// 失败部署次数
	var failedDeployments int64
	database.DB.Model(&models.Deployment{}).Where("status = ?", "failed").Count(&failedDeployments)
	stats["failed_deployments"] = failedDeployments

	// 待审核部署次数
	var pendingDeployments int64
	database.DB.Model(&models.Deployment{}).Where("status = ?", "pending").Count(&pendingDeployments)
	stats["pending_deployments"] = pendingDeployments

	// 成功率
	if totalDeployments > 0 {
		stats["success_rate"] = float64(successDeployments) / float64(totalDeployments) * 100
	} else {
		stats["success_rate"] = 0
	}

	// 最近7天的部署趋势
	var dailyStats []map[string]interface{}
	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i)
		dateStr := date.Format("2006-01-02")
		
		var dayTotal int64
		var daySuccess int64
		
		database.DB.Model(&models.Deployment{}).
			Where("DATE(created_at) = ?", dateStr).
			Count(&dayTotal)
			
		database.DB.Model(&models.Deployment{}).
			Where("DATE(created_at) = ? AND status = ?", dateStr, "success").
			Count(&daySuccess)
		
		dailyStats = append(dailyStats, map[string]interface{}{
			"date":    dateStr,
			"total":   dayTotal,
			"success": daySuccess,
		})
	}
	stats["daily_stats"] = dailyStats

	return stats
}
