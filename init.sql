-- 初始化数据库脚本
CREATE DATABASE IF NOT EXISTS jen<PERSON>_deploy CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE jenkins_deploy;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    role ENUM('admin', 'user', 'reviewer') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 创建项目表
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    git_url VARCHAR(255) NOT NULL,
    branch VARCHAR(50) DEFAULT 'master',
    jenkins_job VARCHAR(100),
    environment ENUM('dev', 'test', 'prod') DEFAULT 'dev',
    status ENUM('active', 'inactive') DEFAULT 'active',
    owner_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 创建部署表
CREATE TABLE IF NOT EXISTS deployments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    version VARCHAR(50) NOT NULL,
    branch VARCHAR(50),
    commit_hash VARCHAR(40),
    environment VARCHAR(20) NOT NULL,
    status ENUM('pending', 'approved', 'rejected', 'deploying', 'success', 'failed', 'cancelled') DEFAULT 'pending',
    jenkins_build INT,
    build_url VARCHAR(255),
    applicant_id INT NOT NULL,
    reviewer_id INT,
    review_comment TEXT,
    reviewed_at TIMESTAMP NULL,
    deployed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (applicant_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 创建部署日志表
CREATE TABLE IF NOT EXISTS deployment_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    deployment_id INT NOT NULL,
    stage VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    level ENUM('info', 'warn', 'error') DEFAULT 'info',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (deployment_id) REFERENCES deployments(id) ON DELETE CASCADE
);

-- 创建Jenkins参数表
CREATE TABLE IF NOT EXISTS jenkins_parameters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    name VARCHAR(100) NOT NULL,
    type ENUM('string', 'choice', 'boolean', 'password') DEFAULT 'string',
    value TEXT,
    options TEXT,
    required BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    UNIQUE KEY unique_project_param (project_id, name)
);

-- 插入默认管理员用户
INSERT INTO users (username, email, password, role) VALUES 
('admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin')
ON DUPLICATE KEY UPDATE username=username;

-- 创建索引
CREATE INDEX idx_deployments_status ON deployments(status);
CREATE INDEX idx_deployments_environment ON deployments(environment);
CREATE INDEX idx_deployments_created_at ON deployments(created_at);
CREATE INDEX idx_deployment_logs_deployment_id ON deployment_logs(deployment_id);
CREATE INDEX idx_projects_owner_id ON projects(owner_id);
CREATE INDEX idx_jenkins_parameters_project_id ON jenkins_parameters(project_id);
