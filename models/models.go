package models

import (
	"time"
)

// User 用户模型
type User struct {
	ID        uint      `gorm:"primary_key" json:"id"`
	Username  string    `gorm:"unique;not null" json:"username"`
	Email     string    `gorm:"unique;not null" json:"email"`
	Password  string    `gorm:"not null" json:"-"`
	Role      string    `gorm:"default:'user'" json:"role"` // admin, user, reviewer
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// Project 项目模型
type Project struct {
	ID          uint      `gorm:"primary_key" json:"id"`
	Name        string    `gorm:"unique;not null" json:"name"`
	Description string    `json:"description"`
	GitURL      string    `gorm:"not null" json:"git_url"`
	Branch      string    `gorm:"default:'master'" json:"branch"`
	JenkinsJob  string    `json:"jenkins_job"`
	Environment string    `gorm:"default:'dev'" json:"environment"` // dev, test, prod
	Status      string    `gorm:"default:'active'" json:"status"`   // active, inactive
	OwnerID     uint      `json:"owner_id"`
	Owner       User      `gorm:"foreignkey:OwnerID" json:"owner"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Deployment 部署记录模型
type Deployment struct {
	ID            uint       `gorm:"primary_key" json:"id"`
	ProjectID     uint       `json:"project_id"`
	Project       Project    `gorm:"foreignkey:ProjectID" json:"project"`
	Version       string     `json:"version"`
	Branch        string     `json:"branch"`
	CommitHash    string     `json:"commit_hash"`
	Environment   string     `json:"environment"`
	Status        string     `gorm:"default:'pending'" json:"status"` // pending, approved, rejected, deploying, success, failed
	JenkinsBuild  int        `json:"jenkins_build"`
	BuildURL      string     `json:"build_url"`
	ApplicantID   uint       `json:"applicant_id"`
	Applicant     User       `gorm:"foreignkey:ApplicantID" json:"applicant"`
	ReviewerID    *uint      `json:"reviewer_id"`
	Reviewer      *User      `gorm:"foreignkey:ReviewerID" json:"reviewer"`
	ReviewComment string     `json:"review_comment"`
	ReviewedAt    *time.Time `json:"reviewed_at"`
	DeployedAt    *time.Time `json:"deployed_at"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
}

// DeploymentLog 部署日志模型
type DeploymentLog struct {
	ID           uint       `gorm:"primary_key" json:"id"`
	DeploymentID uint       `json:"deployment_id"`
	Deployment   Deployment `gorm:"foreignkey:DeploymentID" json:"deployment"`
	Stage        string     `json:"stage"`
	Message      string     `gorm:"type:text" json:"message"`
	Level        string     `gorm:"default:'info'" json:"level"` // info, warn, error
	CreatedAt    time.Time  `json:"created_at"`
}

// JenkinsParameter Jenkins参数模型
type JenkinsParameter struct {
	ID        uint      `gorm:"primary_key" json:"id"`
	ProjectID uint      `json:"project_id"`
	Project   Project   `gorm:"foreignkey:ProjectID" json:"project"`
	Name      string    `gorm:"not null" json:"name"`
	Type      string    `gorm:"default:'string'" json:"type"` // string, choice, boolean, password
	Value     string    `json:"value"`
	Options   string    `json:"options"` // for choice type, comma separated
	Required  bool      `gorm:"default:false" json:"required"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
