# Jenkins 部署平台

基于 Go Gin 框架和 Jenkins 的自动化部署平台，支持项目管理、多参数构建、审核流程等功能。

## 功能特性

### 核心功能
- 🚀 **项目管理**: 创建、配置和管理部署项目
- 🔧 **Jenkins 集成**: 自动创建和管理 Jenkins 任务
- 📋 **多参数构建**: 支持字符串、选择、布尔等多种参数类型
- 👥 **审核流程**: 完整的部署申请和审核机制
- 📊 **部署监控**: 实时监控部署状态和日志
- 🔐 **权限控制**: 基于角色的访问控制（管理员、审核员、用户）

### 技术特性
- RESTful API 设计
- JWT 身份认证
- MySQL 数据持久化
- Docker 容器化部署
- 优雅的错误处理和日志记录

## 快速开始

### 环境要求
- Go 1.21+
- MySQL 8.0+
- Jenkins 2.4+
- Docker & Docker Compose (可选)

### 使用 Docker Compose 启动

1. 克隆项目
```bash
git clone <repository-url>
cd jenkins-deploy-platform
```

2. 启动服务
```bash
docker-compose up -d
```

3. 访问服务
- 应用服务: http://localhost:8888
- Jenkins: http://localhost:8080
- MySQL: localhost:3306

### 手动部署

1. 安装依赖
```bash
go mod download
```

2. 配置数据库
```bash
# 创建数据库
mysql -u root -p < init.sql
```

3. 修改配置文件
```bash
cp config/config.yaml.example config/config.yaml
# 编辑配置文件，设置数据库和Jenkins连接信息
```

4. 运行应用
```bash
go run main.go
```

## API 文档

### 认证接口

#### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "password"
}
```

#### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "user"
}
```

### 项目管理

#### 创建项目
```http
POST /api/v1/projects
Authorization: Bearer <token>
Content-Type: application/json

{
  "name": "my-project",
  "description": "项目描述",
  "git_url": "https://github.com/user/repo.git",
  "branch": "master",
  "environment": "dev"
}
```

#### 获取项目列表
```http
GET /api/v1/projects?page=1&page_size=10&environment=dev
Authorization: Bearer <token>
```

### 参数管理

#### 创建Jenkins参数
```http
POST /api/v1/parameters
Authorization: Bearer <token>
Content-Type: application/json

{
  "project_id": 1,
  "name": "BUILD_TYPE",
  "type": "choice",
  "value": "release",
  "options": "debug,release,test",
  "required": true
}
```

### 部署管理

#### 创建部署申请
```http
POST /api/v1/deployments
Authorization: Bearer <token>
Content-Type: application/json

{
  "project_id": 1,
  "version": "v1.0.0",
  "branch": "master",
  "environment": "prod",
  "parameters": {
    "BUILD_TYPE": "release",
    "ENABLE_TESTS": "true"
  }
}
```

#### 审核部署
```http
PUT /api/v1/deployments/1/review
Authorization: Bearer <token>
Content-Type: application/json

{
  "status": "approved",
  "comment": "审核通过"
}
```

## 配置说明

### 配置文件结构
```yaml
server:
  port: "8080"
  mode: "debug"

database:
  host: "localhost"
  port: "3306"
  username: "root"
  password: "password"
  database: "jenkins_deploy"
  charset: "utf8mb4"

jenkins:
  url: "http://localhost:8080"
  username: "admin"
  token: "your-jenkins-api-token"

jwt:
  secret: "your-jwt-secret-key"
  expire_time: 24
```

### Jenkins 配置

1. 安装必要插件
   - Git Plugin
   - Build Authorization Token Root Plugin
   - Parameterized Trigger Plugin

2. 创建API Token
   - 登录Jenkins
   - 用户设置 -> API Token -> 添加新Token

3. 配置权限
   - 确保API用户有创建和构建任务的权限

## 数据库设计

### 主要表结构

- `users`: 用户表
- `projects`: 项目表
- `deployments`: 部署记录表
- `deployment_logs`: 部署日志表
- `jenkins_parameters`: Jenkins参数表

## 部署流程

1. **创建项目**: 用户创建项目并配置Git仓库
2. **配置参数**: 设置Jenkins构建参数
3. **申请部署**: 用户提交部署申请
4. **审核流程**: 
   - 开发环境: 自动审批
   - 测试/生产环境: 需要审核员审批
5. **执行部署**: 触发Jenkins构建任务
6. **监控状态**: 实时监控构建状态和日志

## 权限说明

### 角色权限
- **admin**: 系统管理员，拥有所有权限
- **reviewer**: 审核员，可以审核部署申请
- **user**: 普通用户，可以创建项目和申请部署

### 操作权限
- 项目管理: 项目所有者和管理员
- 部署申请: 所有认证用户
- 部署审核: 审核员和管理员
- 系统管理: 仅管理员

## 开发指南

### 项目结构
```
├── config/          # 配置文件
├── controllers/     # 控制器
├── database/        # 数据库连接
├── middleware/      # 中间件
├── models/          # 数据模型
├── routes/          # 路由定义
├── services/        # 业务逻辑
├── utils/           # 工具函数
├── main.go          # 应用入口
└── docker-compose.yml
```

### 添加新功能
1. 在 `models/` 中定义数据模型
2. 在 `controllers/` 中实现业务逻辑
3. 在 `routes/` 中添加路由
4. 在 `services/` 中实现复杂业务逻辑

## 故障排除

### 常见问题

1. **Jenkins连接失败**
   - 检查Jenkins URL和认证信息
   - 确认Jenkins API Token有效
   - 检查网络连接

2. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务运行正常
   - 检查用户权限

3. **部署失败**
   - 查看部署日志
   - 检查Jenkins任务配置
   - 确认Git仓库访问权限

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License
