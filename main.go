package main

import (
	"log"
	"os"
	"os/signal"
	"syscall"

	"jenkins-deploy-platform/config"
	"jenkins-deploy-platform/database"
	"jenkins-deploy-platform/routes"

	"github.com/gin-gonic/gin"
)

func main() {
	// 初始化配置
	config.InitConfig()

	// 设置Gin模式
	gin.SetMode(config.AppConfig.Server.Mode)

	// 初始化数据库
	database.InitDatabase()
	defer database.CloseDatabase()

	// 创建Gin引擎
	r := gin.Default()

	// 设置路由
	routes.SetupRoutes(r)

	// 启动服务器
	port := config.AppConfig.Server.Port
	if port == "" {
		port = "8080"
	}

	log.Printf("Server starting on port %s", port)
	log.Printf("Server mode: %s", config.AppConfig.Server.Mode)
	log.Printf("Jenkins URL: %s", config.AppConfig.Jenkins.URL)

	// 优雅关闭
	go func() {
		if err := r.Run(":" + port); err != nil {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Server shutting down...")
}
