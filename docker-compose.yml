version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: jenkins-deploy-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: jenkins_deploy
      MYSQL_USER: deploy_user
      MYSQL_PASSWORD: deploy_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - jenkins-deploy-network

  jenkins:
    image: jenkins/jenkins:lts
    container_name: jenkins-deploy-jenkins
    user: root
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      - jenkins_data:/var/jenkins_home
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - JENKINS_OPTS=--httpPort=8080
    networks:
      - jenkins-deploy-network

  app:
    build: .
    container_name: jenkins-deploy-app
    ports:
      - "8888:8080"
    depends_on:
      - mysql
      - jenkins
    environment:
      - GIN_MODE=release
    volumes:
      - ./config:/app/config
    networks:
      - jenkins-deploy-network
    restart: unless-stopped

volumes:
  mysql_data:
  jenkins_data:

networks:
  jenkins-deploy-network:
    driver: bridge
