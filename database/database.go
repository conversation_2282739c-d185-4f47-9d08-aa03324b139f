package database

import (
	"fmt"
	"log"

	"jenkins-deploy-platform/config"
	"jenkins-deploy-platform/models"

	"github.com/jinzhu/gorm"
	_ "github.com/go-sql-driver/mysql"
)

var DB *gorm.DB

func InitDatabase() {
	cfg := config.AppConfig.Database
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
		cfg.Username,
		cfg.Password,
		cfg.Host,
		cfg.Port,
		cfg.Database,
		cfg.Charset,
	)

	var err error
	DB, err = gorm.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("Failed to connect to database: %v", err)
	}

	// 设置连接池
	DB.DB().SetMaxIdleConns(10)
	DB.DB().SetMaxOpenConns(100)

	// 自动迁移
	autoMigrate()

	log.Println("Database connected successfully")
}

func autoMigrate() {
	DB.AutoMigrate(
		&models.User{},
		&models.Project{},
		&models.Deployment{},
		&models.DeploymentLog{},
		&models.JenkinsParameter{},
	)

	// 创建默认管理员用户
	createDefaultAdmin()
}

func createDefaultAdmin() {
	var count int
	DB.Model(&models.User{}).Count(&count)
	
	if count == 0 {
		admin := models.User{
			Username: "admin",
			Email:    "<EMAIL>",
			Password: "$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi", // password
			Role:     "admin",
		}
		DB.Create(&admin)
		log.Println("Default admin user created: admin/password")
	}
}

func CloseDatabase() {
	if DB != nil {
		DB.Close()
	}
}
