package routes

import (
	"jenkins-deploy-platform/controllers"
	"jenkins-deploy-platform/middleware"
	"jenkins-deploy-platform/services"

	"github.com/gin-gonic/gin"
)

func SetupRoutes(r *gin.Engine) {
	// 初始化控制器
	authController := &controllers.AuthController{}
	projectController := &controllers.ProjectController{}
	deploymentController := &controllers.DeploymentController{}
	parameterController := &controllers.ParameterController{}

	// 添加中间件
	r.Use(middleware.CORSMiddleware())

	// 公开路由
	public := r.Group("/api/v1")
	{
		// 认证相关
		public.POST("/auth/login", authController.Login)
		public.POST("/auth/register", authController.Register)
		
		// 健康检查
		public.GET("/health", func(c *gin.Context) {
			c.JSON(200, gin.H{"status": "ok"})
		})
	}

	// 需要认证的路由
	protected := r.Group("/api/v1")
	protected.Use(middleware.AuthMiddleware())
	{
		// 用户相关
		protected.GET("/auth/profile", authController.GetProfile)

		// 项目管理
		projects := protected.Group("/projects")
		{
			projects.POST("", projectController.CreateProject)
			projects.GET("", projectController.GetProjects)
			projects.GET("/:id", projectController.GetProject)
			projects.PUT("/:id", projectController.UpdateProject)
			projects.DELETE("/:id", projectController.DeleteProject)
			
			// 项目参数管理
			projects.GET("/:project_id/parameters", parameterController.GetParametersByProject)
		}

		// 参数管理
		parameters := protected.Group("/parameters")
		{
			parameters.POST("", parameterController.CreateParameter)
			parameters.GET("", parameterController.GetParameters)
			parameters.GET("/:id", parameterController.GetParameter)
			parameters.PUT("/:id", parameterController.UpdateParameter)
			parameters.DELETE("/:id", parameterController.DeleteParameter)
		}

		// 部署管理
		deployments := protected.Group("/deployments")
		{
			deployments.POST("", deploymentController.CreateDeployment)
			deployments.GET("", deploymentController.GetDeployments)
			deployments.GET("/:id", deploymentController.GetDeployment)
			deployments.PUT("/:id/cancel", deploymentController.CancelDeployment)
			deployments.GET("/:id/logs", deploymentController.GetDeploymentLogs)
			
			// 统计信息
			deployments.GET("/statistics", func(c *gin.Context) {
				stats := services.GetDeploymentStatistics()
				c.JSON(200, stats)
			})
		}

		// 需要审核员权限的路由
		reviewer := protected.Group("/deployments")
		reviewer.Use(middleware.ReviewerMiddleware())
		{
			reviewer.PUT("/:id/review", deploymentController.ReviewDeployment)
		}
	}

	// 管理员路由
	admin := r.Group("/api/v1/admin")
	admin.Use(middleware.AuthMiddleware())
	admin.Use(middleware.AdminMiddleware())
	{
		// 用户管理
		admin.GET("/users", func(c *gin.Context) {
			// TODO: 实现用户管理功能
			c.JSON(200, gin.H{"message": "User management endpoint"})
		})
		
		// 系统统计
		admin.GET("/statistics", func(c *gin.Context) {
			stats := services.GetDeploymentStatistics()
			c.JSON(200, stats)
		})
	}
}
