package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	"jenkins-deploy-platform/config"
)

type JenkinsClient struct {
	BaseURL  string
	Username string
	Token    string
	Client   *http.Client
}

type JenkinsBuild struct {
	Number    int    `json:"number"`
	URL       string `json:"url"`
	Result    string `json:"result"`
	Building  bool   `json:"building"`
	Timestamp int64  `json:"timestamp"`
}

type JenkinsJob struct {
	Name        string         `json:"name"`
	URL         string         `json:"url"`
	Buildable   bool           `json:"buildable"`
	LastBuild   *JenkinsBuild  `json:"lastBuild"`
	Builds      []JenkinsBuild `json:"builds"`
}

func NewJenkinsClient() *JenkinsClient {
	cfg := config.AppConfig.Jenkins
	return &JenkinsClient{
		BaseURL:  cfg.URL,
		Username: cfg.Username,
		Token:    cfg.Token,
		Client:   &http.Client{},
	}
}

func (j *JenkinsClient) makeRequest(method, path string, body io.Reader) (*http.Response, error) {
	url := fmt.Sprintf("%s%s", j.<PERSON>, path)
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}

	req.SetBasicAuth(j.Username, j.Token)
	req.Header.Set("Content-Type", "application/json")

	return j.Client.Do(req)
}

// CreateJob 创建Jenkins任务
func (j *JenkinsClient) CreateJob(jobName, configXML string) error {
	path := fmt.Sprintf("/createItem?name=%s", url.QueryEscape(jobName))
	resp, err := j.makeRequest("POST", path, strings.NewReader(configXML))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("failed to create job: %s", string(body))
	}

	return nil
}

// BuildJob 触发构建任务
func (j *JenkinsClient) BuildJob(jobName string, parameters map[string]string) (int, error) {
	var path string
	if len(parameters) > 0 {
		// 带参数构建
		path = fmt.Sprintf("/job/%s/buildWithParameters", url.QueryEscape(jobName))
		
		// 构建参数
		values := url.Values{}
		for key, value := range parameters {
			values.Set(key, value)
		}
		
		resp, err := j.makeRequest("POST", path, strings.NewReader(values.Encode()))
		if err != nil {
			return 0, err
		}
		defer resp.Body.Close()
	} else {
		// 无参数构建
		path = fmt.Sprintf("/job/%s/build", url.QueryEscape(jobName))
		resp, err := j.makeRequest("POST", path, nil)
		if err != nil {
			return 0, err
		}
		defer resp.Body.Close()
	}

	// 获取构建号
	return j.getNextBuildNumber(jobName)
}

// GetBuildStatus 获取构建状态
func (j *JenkinsClient) GetBuildStatus(jobName string, buildNumber int) (*JenkinsBuild, error) {
	path := fmt.Sprintf("/job/%s/%d/api/json", url.QueryEscape(jobName), buildNumber)
	resp, err := j.makeRequest("GET", path, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("build not found")
	}

	var build JenkinsBuild
	if err := json.NewDecoder(resp.Body).Decode(&build); err != nil {
		return nil, err
	}

	return &build, nil
}

// GetBuildLog 获取构建日志
func (j *JenkinsClient) GetBuildLog(jobName string, buildNumber int) (string, error) {
	path := fmt.Sprintf("/job/%s/%d/consoleText", url.QueryEscape(jobName), buildNumber)
	resp, err := j.makeRequest("GET", path, nil)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}

func (j *JenkinsClient) getNextBuildNumber(jobName string) (int, error) {
	path := fmt.Sprintf("/job/%s/api/json", url.QueryEscape(jobName))
	resp, err := j.makeRequest("GET", path, nil)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	var job JenkinsJob
	if err := json.NewDecoder(resp.Body).Decode(&job); err != nil {
		return 0, err
	}

	if job.LastBuild != nil {
		return job.LastBuild.Number + 1, nil
	}

	return 1, nil
}

// GenerateJobConfig 生成Jenkins任务配置XML
func GenerateJobConfig(gitURL, branch string, parameters []map[string]string) string {
	parameterDefinitions := ""
	for _, param := range parameters {
		switch param["type"] {
		case "string":
			parameterDefinitions += fmt.Sprintf(`
				<hudson.model.StringParameterDefinition>
					<name>%s</name>
					<description>%s</description>
					<defaultValue>%s</defaultValue>
				</hudson.model.StringParameterDefinition>`,
				param["name"], param["description"], param["defaultValue"])
		case "choice":
			choices := strings.Split(param["choices"], ",")
			choiceList := ""
			for _, choice := range choices {
				choiceList += fmt.Sprintf("<string>%s</string>", strings.TrimSpace(choice))
			}
			parameterDefinitions += fmt.Sprintf(`
				<hudson.model.ChoiceParameterDefinition>
					<name>%s</name>
					<description>%s</description>
					<choices class="java.util.Arrays$ArrayList">
						<a class="string-array">%s</a>
					</choices>
				</hudson.model.ChoiceParameterDefinition>`,
				param["name"], param["description"], choiceList)
		case "boolean":
			defaultValue := param["defaultValue"] == "true"
			parameterDefinitions += fmt.Sprintf(`
				<hudson.model.BooleanParameterDefinition>
					<name>%s</name>
					<description>%s</description>
					<defaultValue>%t</defaultValue>
				</hudson.model.BooleanParameterDefinition>`,
				param["name"], param["description"], defaultValue)
		}
	}

	config := fmt.Sprintf(`<?xml version='1.1' encoding='UTF-8'?>
<project>
	<actions/>
	<description>Auto-generated deployment job</description>
	<keepDependencies>false</keepDependencies>
	<properties>
		<hudson.model.ParametersDefinitionProperty>
			<parameterDefinitions>%s</parameterDefinitions>
		</hudson.model.ParametersDefinitionProperty>
	</properties>
	<scm class="hudson.plugins.git.GitSCM">
		<configVersion>2</configVersion>
		<userRemoteConfigs>
			<hudson.plugins.git.UserRemoteConfig>
				<url>%s</url>
			</hudson.plugins.git.UserRemoteConfig>
		</userRemoteConfigs>
		<branches>
			<hudson.plugins.git.BranchSpec>
				<name>*/%s</name>
			</hudson.plugins.git.BranchSpec>
		</branches>
	</scm>
	<builders>
		<hudson.tasks.Shell>
			<command>echo "Starting deployment..."
echo "Branch: %s"
echo "Parameters:"
env | grep -E '^[A-Z_]+'
echo "Deployment completed successfully"</command>
		</hudson.tasks.Shell>
	</builders>
	<publishers/>
	<buildWrappers/>
</project>`, parameterDefinitions, gitURL, branch, branch)

	return config
}
