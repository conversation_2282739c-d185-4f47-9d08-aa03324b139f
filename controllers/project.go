package controllers

import (
	"net/http"
	"strconv"

	"jenkins-deploy-platform/database"
	"jenkins-deploy-platform/models"
	"jenkins-deploy-platform/utils"

	"github.com/gin-gonic/gin"
)

type ProjectController struct{}

type CreateProjectRequest struct {
	Name        string `json:"name" binding:"required"`
	Description string `json:"description"`
	GitURL      string `json:"git_url" binding:"required"`
	Branch      string `json:"branch"`
	Environment string `json:"environment"`
}

type UpdateProjectRequest struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	GitURL      string `json:"git_url"`
	Branch      string `json:"branch"`
	Environment string `json:"environment"`
	Status      string `json:"status"`
}

// CreateProject 创建项目
func (pc *ProjectController) CreateProject(c *gin.Context) {
	var req CreateProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSO<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, _ := c.Get("user_id")

	// 检查项目名是否已存在
	var existingProject models.Project
	if err := database.DB.Where("name = ?", req.Name).First(&existingProject).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Project name already exists"})
		return
	}

	// 设置默认值
	if req.Branch == "" {
		req.Branch = "master"
	}
	if req.Environment == "" {
		req.Environment = "dev"
	}

	project := models.Project{
		Name:        req.Name,
		Description: req.Description,
		GitURL:      req.GitURL,
		Branch:      req.Branch,
		Environment: req.Environment,
		JenkinsJob:  req.Name + "-deploy",
		OwnerID:     userID.(uint),
	}

	if err := database.DB.Create(&project).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create project"})
		return
	}

	// 创建Jenkins任务
	go pc.createJenkinsJob(&project)

	// 加载关联数据
	database.DB.Preload("Owner").First(&project, project.ID)

	c.JSON(http.StatusCreated, project)
}

// GetProjects 获取项目列表
func (pc *ProjectController) GetProjects(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	environment := c.Query("environment")
	status := c.Query("status")

	offset := (page - 1) * pageSize

	query := database.DB.Model(&models.Project{}).Preload("Owner")

	if environment != "" {
		query = query.Where("environment = ?", environment)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}

	var projects []models.Project
	var total int64

	query.Count(&total)
	query.Offset(offset).Limit(pageSize).Find(&projects)

	c.JSON(http.StatusOK, gin.H{
		"projects": projects,
		"total":    total,
		"page":     page,
		"page_size": pageSize,
	})
}

// GetProject 获取单个项目
func (pc *ProjectController) GetProject(c *gin.Context) {
	id := c.Param("id")
	
	var project models.Project
	if err := database.DB.Preload("Owner").First(&project, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	c.JSON(http.StatusOK, project)
}

// UpdateProject 更新项目
func (pc *ProjectController) UpdateProject(c *gin.Context) {
	id := c.Param("id")
	
	var project models.Project
	if err := database.DB.First(&project, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	// 检查权限
	userID, _ := c.Get("user_id")
	role, _ := c.Get("role")
	if role != "admin" && project.OwnerID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req UpdateProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新字段
	if req.Name != "" {
		project.Name = req.Name
	}
	if req.Description != "" {
		project.Description = req.Description
	}
	if req.GitURL != "" {
		project.GitURL = req.GitURL
	}
	if req.Branch != "" {
		project.Branch = req.Branch
	}
	if req.Environment != "" {
		project.Environment = req.Environment
	}
	if req.Status != "" {
		project.Status = req.Status
	}

	if err := database.DB.Save(&project).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update project"})
		return
	}

	// 加载关联数据
	database.DB.Preload("Owner").First(&project, project.ID)

	c.JSON(http.StatusOK, project)
}

// DeleteProject 删除项目
func (pc *ProjectController) DeleteProject(c *gin.Context) {
	id := c.Param("id")
	
	var project models.Project
	if err := database.DB.First(&project, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	// 检查权限
	userID, _ := c.Get("user_id")
	role, _ := c.Get("role")
	if role != "admin" && project.OwnerID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	if err := database.DB.Delete(&project).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete project"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Project deleted successfully"})
}

// createJenkinsJob 创建Jenkins任务
func (pc *ProjectController) createJenkinsJob(project *models.Project) {
	client := utils.NewJenkinsClient()
	
	// 获取项目参数
	var parameters []models.JenkinsParameter
	database.DB.Where("project_id = ?", project.ID).Find(&parameters)
	
	// 转换参数格式
	paramMap := make([]map[string]string, 0)
	for _, param := range parameters {
		paramMap = append(paramMap, map[string]string{
			"name":         param.Name,
			"type":         param.Type,
			"description":  param.Name + " parameter",
			"defaultValue": param.Value,
			"choices":      param.Options,
		})
	}
	
	// 生成Jenkins配置
	config := utils.GenerateJobConfig(project.GitURL, project.Branch, paramMap)
	
	// 创建Jenkins任务
	if err := client.CreateJob(project.JenkinsJob, config); err != nil {
		// 记录错误日志
		// log.Printf("Failed to create Jenkins job for project %s: %v", project.Name, err)
	}
}
