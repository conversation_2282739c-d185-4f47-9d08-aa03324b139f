package controllers

import (
	"net/http"
	"strconv"
	"time"

	"jenkins-deploy-platform/database"
	"jenkins-deploy-platform/models"
	"jenkins-deploy-platform/services"

	"github.com/gin-gonic/gin"
)

type DeploymentController struct{}

type CreateDeploymentRequest struct {
	ProjectID   uint              `json:"project_id" binding:"required"`
	Version     string            `json:"version" binding:"required"`
	Branch      string            `json:"branch"`
	CommitHash  string            `json:"commit_hash"`
	Environment string            `json:"environment" binding:"required"`
	Parameters  map[string]string `json:"parameters"`
}

type ReviewDeploymentRequest struct {
	Status  string `json:"status" binding:"required,oneof=approved rejected"`
	Comment string `json:"comment"`
}

// CreateDeployment 创建部署申请
func (dc *DeploymentController) CreateDeployment(c *gin.Context) {
	var req CreateDeploymentRequest
	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, _ := c.Get("user_id")

	// 检查项目是否存在
	var project models.Project
	if err := database.DB.First(&project, req.ProjectID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	// 设置默认分支
	if req.Branch == "" {
		req.Branch = project.Branch
	}

	deployment := models.Deployment{
		ProjectID:   req.ProjectID,
		Version:     req.Version,
		Branch:      req.Branch,
		CommitHash:  req.CommitHash,
		Environment: req.Environment,
		Status:      "pending",
		ApplicantID: userID.(uint),
	}

	if err := database.DB.Create(&deployment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create deployment"})
		return
	}

	// 记录部署日志
	services.LogDeployment(deployment.ID, "create", "Deployment request created", "info")

	// 如果是开发环境，自动审批
	if req.Environment == "dev" {
		go dc.autoApproveDevDeployment(deployment.ID, req.Parameters)
	}

	// 加载关联数据
	database.DB.Preload("Project").Preload("Applicant").First(&deployment, deployment.ID)

	c.JSON(http.StatusCreated, deployment)
}

// GetDeployments 获取部署列表
func (dc *DeploymentController) GetDeployments(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	projectID := c.Query("project_id")
	status := c.Query("status")
	environment := c.Query("environment")

	offset := (page - 1) * pageSize

	query := database.DB.Model(&models.Deployment{}).
		Preload("Project").
		Preload("Applicant").
		Preload("Reviewer")

	if projectID != "" {
		query = query.Where("project_id = ?", projectID)
	}
	if status != "" {
		query = query.Where("status = ?", status)
	}
	if environment != "" {
		query = query.Where("environment = ?", environment)
	}

	var deployments []models.Deployment
	var total int64

	query.Count(&total)
	query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&deployments)

	c.JSON(http.StatusOK, gin.H{
		"deployments": deployments,
		"total":       total,
		"page":        page,
		"page_size":   pageSize,
	})
}

// GetDeployment 获取单个部署
func (dc *DeploymentController) GetDeployment(c *gin.Context) {
	id := c.Param("id")

	var deployment models.Deployment
	if err := database.DB.Preload("Project").Preload("Applicant").Preload("Reviewer").First(&deployment, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Deployment not found"})
		return
	}

	c.JSON(http.StatusOK, deployment)
}

// ReviewDeployment 审核部署
func (dc *DeploymentController) ReviewDeployment(c *gin.Context) {
	id := c.Param("id")

	var deployment models.Deployment
	if err := database.DB.First(&deployment, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Deployment not found"})
		return
	}

	if deployment.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Deployment is not pending review"})
		return
	}

	var req ReviewDeploymentRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	userID, _ := c.Get("user_id")
	now := time.Now()

	deployment.Status = req.Status
	deployment.ReviewComment = req.Comment
	deployment.ReviewerID = &[]uint{userID.(uint)}[0]
	deployment.ReviewedAt = &now

	if err := database.DB.Save(&deployment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update deployment"})
		return
	}

	// 记录审核日志
	services.LogDeployment(deployment.ID, "review", "Deployment "+req.Status+" by reviewer", "info")

	// 如果审批通过，开始部署
	if req.Status == "approved" {
		go services.StartDeployment(deployment.ID)
	}

	// 加载关联数据
	database.DB.Preload("Project").Preload("Applicant").Preload("Reviewer").First(&deployment, deployment.ID)

	c.JSON(http.StatusOK, deployment)
}

// GetDeploymentLogs 获取部署日志
func (dc *DeploymentController) GetDeploymentLogs(c *gin.Context) {
	id := c.Param("id")

	var logs []models.DeploymentLog
	if err := database.DB.Where("deployment_id = ?", id).Order("created_at ASC").Find(&logs).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get logs"})
		return
	}

	c.JSON(http.StatusOK, logs)
}

// CancelDeployment 取消部署
func (dc *DeploymentController) CancelDeployment(c *gin.Context) {
	id := c.Param("id")

	var deployment models.Deployment
	if err := database.DB.First(&deployment, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Deployment not found"})
		return
	}

	userID, _ := c.Get("user_id")
	role, _ := c.Get("role")

	// 检查权限：申请人或管理员可以取消
	if role != "admin" && deployment.ApplicantID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	// 只有pending状态的部署可以取消
	if deployment.Status != "pending" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Only pending deployments can be cancelled"})
		return
	}

	deployment.Status = "cancelled"
	if err := database.DB.Save(&deployment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to cancel deployment"})
		return
	}

	// 记录取消日志
	services.LogDeployment(deployment.ID, "cancel", "Deployment cancelled by user", "info")

	c.JSON(http.StatusOK, gin.H{"message": "Deployment cancelled successfully"})
}

// autoApproveDevDeployment 自动审批开发环境部署
func (dc *DeploymentController) autoApproveDevDeployment(deploymentID uint, parameters map[string]string) {
	time.Sleep(1 * time.Second) // 短暂延迟

	var deployment models.Deployment
	if err := database.DB.First(&deployment, deploymentID).Error; err != nil {
		return
	}

	if deployment.Status != "pending" {
		return
	}

	// 自动审批
	now := time.Now()
	deployment.Status = "approved"
	deployment.ReviewComment = "Auto-approved for development environment"
	deployment.ReviewedAt = &now

	database.DB.Save(&deployment)

	// 记录日志
	services.LogDeployment(deploymentID, "auto-approve", "Auto-approved for dev environment", "info")

	// 开始部署
	services.StartDeploymentWithParams(deploymentID, parameters)
}
