package controllers

import (
	"net/http"
	"strconv"

	"jenkins-deploy-platform/database"
	"jenkins-deploy-platform/models"

	"github.com/gin-gonic/gin"
)

type ParameterController struct{}

type CreateParameterRequest struct {
	ProjectID uint   `json:"project_id" binding:"required"`
	Name      string `json:"name" binding:"required"`
	Type      string `json:"type" binding:"required,oneof=string choice boolean password"`
	Value     string `json:"value"`
	Options   string `json:"options"`
	Required  bool   `json:"required"`
}

type UpdateParameterRequest struct {
	Name     string `json:"name"`
	Type     string `json:"type" binding:"omitempty,oneof=string choice boolean password"`
	Value    string `json:"value"`
	Options  string `json:"options"`
	Required *bool  `json:"required"`
}

// CreateParameter 创建Jenkins参数
func (pc *ParameterController) CreateParameter(c *gin.Context) {
	var req CreateParameterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查项目是否存在
	var project models.Project
	if err := database.DB.First(&project, req.ProjectID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	// 检查权限
	userID, _ := c.Get("user_id")
	role, _ := c.Get("role")
	if role != "admin" && project.OwnerID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	// 检查参数名是否已存在
	var existingParam models.JenkinsParameter
	if err := database.DB.Where("project_id = ? AND name = ?", req.ProjectID, req.Name).First(&existingParam).Error; err == nil {
		c.JSON(http.StatusConflict, gin.H{"error": "Parameter name already exists for this project"})
		return
	}

	parameter := models.JenkinsParameter{
		ProjectID: req.ProjectID,
		Name:      req.Name,
		Type:      req.Type,
		Value:     req.Value,
		Options:   req.Options,
		Required:  req.Required,
	}

	if err := database.DB.Create(&parameter).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create parameter"})
		return
	}

	// 加载关联数据
	database.DB.Preload("Project").First(&parameter, parameter.ID)

	c.JSON(http.StatusCreated, parameter)
}

// GetParameters 获取项目参数列表
func (pc *ParameterController) GetParameters(c *gin.Context) {
	projectID := c.Query("project_id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "project_id is required"})
		return
	}

	var parameters []models.JenkinsParameter
	if err := database.DB.Where("project_id = ?", projectID).Preload("Project").Find(&parameters).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get parameters"})
		return
	}

	c.JSON(http.StatusOK, parameters)
}

// GetParameter 获取单个参数
func (pc *ParameterController) GetParameter(c *gin.Context) {
	id := c.Param("id")

	var parameter models.JenkinsParameter
	if err := database.DB.Preload("Project").First(&parameter, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Parameter not found"})
		return
	}

	c.JSON(http.StatusOK, parameter)
}

// UpdateParameter 更新参数
func (pc *ParameterController) UpdateParameter(c *gin.Context) {
	id := c.Param("id")

	var parameter models.JenkinsParameter
	if err := database.DB.Preload("Project").First(&parameter, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Parameter not found"})
		return
	}

	// 检查权限
	userID, _ := c.Get("user_id")
	role, _ := c.Get("role")
	if role != "admin" && parameter.Project.OwnerID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	var req UpdateParameterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 更新字段
	if req.Name != "" {
		// 检查新名称是否已存在
		var existingParam models.JenkinsParameter
		if err := database.DB.Where("project_id = ? AND name = ? AND id != ?", parameter.ProjectID, req.Name, parameter.ID).First(&existingParam).Error; err == nil {
			c.JSON(http.StatusConflict, gin.H{"error": "Parameter name already exists for this project"})
			return
		}
		parameter.Name = req.Name
	}
	if req.Type != "" {
		parameter.Type = req.Type
	}
	if req.Value != "" {
		parameter.Value = req.Value
	}
	if req.Options != "" {
		parameter.Options = req.Options
	}
	if req.Required != nil {
		parameter.Required = *req.Required
	}

	if err := database.DB.Save(&parameter).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update parameter"})
		return
	}

	// 重新加载关联数据
	database.DB.Preload("Project").First(&parameter, parameter.ID)

	c.JSON(http.StatusOK, parameter)
}

// DeleteParameter 删除参数
func (pc *ParameterController) DeleteParameter(c *gin.Context) {
	id := c.Param("id")

	var parameter models.JenkinsParameter
	if err := database.DB.Preload("Project").First(&parameter, id).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Parameter not found"})
		return
	}

	// 检查权限
	userID, _ := c.Get("user_id")
	role, _ := c.Get("role")
	if role != "admin" && parameter.Project.OwnerID != userID.(uint) {
		c.JSON(http.StatusForbidden, gin.H{"error": "Permission denied"})
		return
	}

	if err := database.DB.Delete(&parameter).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete parameter"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Parameter deleted successfully"})
}

// GetParametersByProject 根据项目ID获取参数（用于部署时选择参数）
func (pc *ParameterController) GetParametersByProject(c *gin.Context) {
	projectID := c.Param("project_id")

	// 检查项目是否存在
	var project models.Project
	if err := database.DB.First(&project, projectID).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Project not found"})
		return
	}

	var parameters []models.JenkinsParameter
	if err := database.DB.Where("project_id = ?", projectID).Find(&parameters).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get parameters"})
		return
	}

	// 格式化参数供前端使用
	formattedParams := make([]map[string]interface{}, 0)
	for _, param := range parameters {
		formatted := map[string]interface{}{
			"id":       param.ID,
			"name":     param.Name,
			"type":     param.Type,
			"value":    param.Value,
			"required": param.Required,
		}

		// 处理选择类型的选项
		if param.Type == "choice" && param.Options != "" {
			formatted["options"] = param.Options
		}

		formattedParams = append(formattedParams, formatted)
	}

	c.JSON(http.StatusOK, gin.H{
		"project":    project,
		"parameters": formattedParams,
	})
}
